
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';

interface SplashScreenProps {
  onComplete: () => void;
}

const SplashScreen = ({ onComplete }: SplashScreenProps) => {
  const [progress, setProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showGetStarted, setShowGetStarted] = useState(true);

  const startLoading = () => {
    setShowGetStarted(false);
    setIsLoading(true);
    setProgress(0);
  };

  useEffect(() => {
    if (!isLoading) return;

    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer);
          setTimeout(onComplete, 500);
          return 100;
        }
        return prev + 2;
      });
    }, 60);

    return () => clearInterval(timer);
  }, [isLoading, onComplete]);

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50"
      style={{
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), url('/lovable-uploads/u5.jpg')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      <div className="text-center animate-fade-in px-4 sm:px-6 lg:px-8 max-w-md mx-auto transition-all duration-500 ease-in-out">
        {/* Logo */}
        <div className="mb-6 sm:mb-8">
          <div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 flex items-center justify-center mx-auto mb-4">
            <img
              src="/lovable-uploads/9c37fb80-a162-4074-bd86-5970c66061e6.png"
              alt="VoiceGuard+"
              className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 filter brightness-0 invert drop-shadow-lg"
              style={{ filter: 'brightness(0) invert(1) drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))' }}
            />
          </div>
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-2 drop-shadow-lg">
            VoiceGuard+
          </h1>
          <p className="text-blue-100 text-base sm:text-lg lg:text-xl font-medium drop-shadow-md">
            Secure Journalism for Zimbabwe
          </p>
        </div>

        {/* Get Started Button or Loading Progress */}
        <div className="w-full max-w-xs sm:max-w-sm mx-auto">
          {showGetStarted ? (
            <div className="text-center animate-fade-in">
              <p className="text-blue-100 text-sm sm:text-base mb-6 drop-shadow-md">
                Your secure journalism platform for Zimbabwe
              </p>
              <Button
                onClick={startLoading}
                size="lg"
                className="bg-white text-primary hover:bg-blue-50 font-semibold px-8 py-3 text-base sm:text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95"
              >
                Get Started
              </Button>
            </div>
          ) : (
            <div className="animate-fade-in">
              <div className="bg-white/20 rounded-full h-2 mb-4 backdrop-blur-sm">
                <div
                  className="bg-white rounded-full h-2 transition-all duration-300 ease-out shadow-sm"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <p className="text-blue-100 text-sm sm:text-base drop-shadow-md">
                Loading your secure platform...
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;
