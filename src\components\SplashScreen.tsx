
import { useEffect, useState } from 'react';

interface SplashScreenProps {
  onComplete: () => void;
}

const SplashScreen = ({ onComplete }: SplashScreenProps) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer);
          setTimeout(onComplete, 500);
          return 100;
        }
        return prev + 2;
      });
    }, 60);

    return () => clearInterval(timer);
  }, [onComplete]);

  return (
    <div className="fixed inset-0 gradient-primary flex items-center justify-center z-50">
      <div className="text-center animate-fade-in">
        {/* Logo */}
        <div className="mb-8">
          <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-2xl">
            <img 
              src="/lovable-uploads/9c37fb80-a162-4074-bd86-5970c66061e6.png"
              alt="VoiceGuard+"
              className="w-16 h-16"
            />
          </div>
          <h1 className="text-4xl font-bold text-white mb-2">VoiceGuard+</h1>
          <p className="text-blue-100 text-lg font-medium">Secure Journalism for Zimbabwe</p>
        </div>

        {/* Loading Progress */}
        <div className="w-64 mx-auto">
          <div className="bg-white/20 rounded-full h-2 mb-4">
            <div 
              className="bg-white rounded-full h-2 transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="text-blue-100 text-sm">Loading your secure platform...</p>
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;
