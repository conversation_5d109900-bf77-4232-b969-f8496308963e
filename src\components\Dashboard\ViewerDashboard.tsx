
import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import StoryCard from '@/components/StoryCard';
import ImageCarousel from '@/components/ImageCarousel';
import { mockStories, featuredSlides } from '@/data/mockData';
import { Search, Filter, TrendingUp, RefreshCw, MapPin, Clock, Bookmark, Share2 } from 'lucide-react';

const ViewerDashboard = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [savedStories, setSavedStories] = useState<string[]>([]);
  
  const categories = ['all', 'Breaking News', 'Community', 'Economy', 'Education', 'Politics', 'Health', 'Environment'];
  const trendingTopics = [
    'Water Crisis Harare', 
    'Local Elections 2024', 
    'Education Reform', 
    'Economic Growth', 
    'Healthcare Access',
    'Infrastructure Development'
  ];

  const filteredStories = mockStories.filter(story => {
    const matchesSearch = story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         story.summary.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         story.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || story.category === selectedCategory;
    return matchesSearch && matchesCategory && story.status === 'published';
  });

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsRefreshing(false);
  };

  const toggleSaveStory = (storyId: string) => {
    setSavedStories(prev => 
      prev.includes(storyId) 
        ? prev.filter(id => id !== storyId)
        : [...prev, storyId]
    );
  };

  const handleShare = (story: any) => {
    if (navigator.share) {
      navigator.share({
        title: story.title,
        text: story.summary,
        url: window.location.href
      });
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white pb-20">
      {/* Hero Section with Carousel */}
      <div className="relative bg-gradient-hero text-white">
        <div className="container-base44 py-6 sm:py-8">
          <div className="text-center mb-6">
            <h1 className="text-display-sm sm:text-display-md font-bold mb-2">
              VoiceGuard+
            </h1>
            <p className="text-lg sm:text-xl text-blue-100 font-medium">
              Mukana WeZimbabwe - Your Voice, Our Platform
            </p>
            <p className="text-sm sm:text-base text-blue-200 mt-2">
              Verified local journalism for Zimbabwe's communities
            </p>
          </div>
        </div>
        
        {/* Featured Stories Carousel */}
        <div className="container-base44 pb-8">
          <ImageCarousel 
            slides={featuredSlides}
            autoPlay={true}
            interval={6000}
            className="animate-fade-in"
          />
        </div>
      </div>

      <div className="container-base44 space-y-6 py-6">
        {/* Search Section */}
        <div className="animate-slide-up">
          <div className="relative max-w-xl mx-auto">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
            <Input
              placeholder="Search stories, locations, topics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="input-base44 pl-12 pr-4 h-12 text-base"
            />
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 animate-slide-up">
          <div className="card-base44 p-4 text-center hover:shadow-lg transition-all duration-300">
            <div className="text-xl sm:text-2xl font-bold text-primary">{mockStories.length}</div>
            <div className="text-xs sm:text-sm text-muted-foreground">Active Stories</div>
          </div>
          <div className="card-base44 p-4 text-center hover:shadow-lg transition-all duration-300">
            <div className="text-xl sm:text-2xl font-bold text-green-600">24/7</div>
            <div className="text-xs sm:text-sm text-muted-foreground">Live Coverage</div>
          </div>
          <div className="card-base44 p-4 text-center hover:shadow-lg transition-all duration-300">
            <div className="text-xl sm:text-2xl font-bold text-orange-600">5</div>
            <div className="text-xs sm:text-sm text-muted-foreground">Provinces</div>
          </div>
          <div className="card-base44 p-4 text-center hover:shadow-lg transition-all duration-300">
            <div className="text-xl sm:text-2xl font-bold text-purple-600">3</div>
            <div className="text-xs sm:text-sm text-muted-foreground">Languages</div>
          </div>
        </div>

        {/* Categories Filter */}
        <div className="animate-slide-up">
          <div className="flex items-center space-x-3 mb-4">
            <Filter className="w-5 h-5 text-primary" />
            <h2 className="text-lg font-semibold">Categories</h2>
          </div>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Badge
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                className={`cursor-pointer touch-target transition-all duration-200 hover:scale-105 ${
                  selectedCategory === category 
                    ? 'bg-primary text-primary-foreground shadow-base44' 
                    : 'hover:bg-primary/10 hover:border-primary'
                }`}
                onClick={() => setSelectedCategory(category)}
              >
                {category === 'all' ? 'All Stories' : category}
              </Badge>
            ))}
          </div>
        </div>

        {/* Trending Topics */}
        <div className="animate-slide-up">
          <div className="flex items-center space-x-3 mb-4">
            <TrendingUp className="w-5 h-5 text-orange-500" />
            <h2 className="text-lg font-semibold">Trending Now</h2>
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2">
            {trendingTopics.map((topic) => (
              <Button
                key={topic}
                variant="outline"
                size="sm"
                className="text-xs h-8 justify-start hover:bg-orange-50 hover:border-orange-200 hover:text-orange-700 transition-all duration-200"
                onClick={() => setSearchQuery(topic)}
              >
                <span className="truncate">{topic}</span>
              </Button>
            ))}
          </div>
        </div>

        {/* Stories Section */}
        <div className="animate-slide-up">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-bold">
                {searchQuery ? `Search Results (${filteredStories.length})` : 'Latest Stories'}
              </h2>
              <p className="text-muted-foreground text-sm">
                {searchQuery 
                  ? `Showing results for "${searchQuery}"` 
                  : 'Stay informed with verified local journalism'
                }
              </p>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
          </div>
          
          {/* Stories Grid */}
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {filteredStories.map((story, index) => (
              <div 
                key={story.id} 
                className="animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="card-base44 hover:shadow-base44-xl transition-all duration-300 group">
                  <StoryCard 
                    story={story}
                    onClick={() => console.log('View story:', story.id)}
                  />
                  
                  {/* Enhanced Story Actions */}
                  <div className="px-4 pb-4 flex items-center justify-between border-t pt-3 mt-3">
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-3 h-3" />
                        <span>{story.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{story.publishedAt}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-blue-50"
                        onClick={() => toggleSaveStory(story.id)}
                      >
                        <Bookmark 
                          className={`w-4 h-4 ${
                            savedStories.includes(story.id) 
                              ? 'fill-blue-500 text-blue-500' 
                              : 'text-muted-foreground'
                          }`} 
                        />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-green-50"
                        onClick={() => handleShare(story)}
                      >
                        <Share2 className="w-4 h-4 text-muted-foreground hover:text-green-600" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Empty State */}
          {filteredStories.length === 0 && (
            <div className="text-center py-16 animate-fade-in">
              <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <Search className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold mb-3">No stories found</h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                We couldn't find any stories matching your search criteria. Try adjusting your filters or search terms.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button 
                  variant="outline" 
                  onClick={() => setSearchQuery('')}
                >
                  Clear Search
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setSelectedCategory('all')}
                >
                  View All Categories
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Load More Button */}
        {filteredStories.length > 0 && (
          <div className="text-center pt-8 animate-fade-in">
            <Button 
              variant="outline" 
              size="lg"
              className="px-8"
            >
              Load More Stories
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ViewerDashboard;
