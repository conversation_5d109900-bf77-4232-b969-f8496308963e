
import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CarouselSlide {
  id: string;
  image: string;
  title: string;
  subtitle: string;
  description: string;
  link?: string;
  category: string;
}

interface ImageCarouselProps {
  slides: CarouselSlide[];
  autoPlay?: boolean;
  interval?: number;
  showControls?: boolean;
  showIndicators?: boolean;
  className?: string;
}

const ImageCarousel = ({ 
  slides, 
  autoPlay = true, 
  interval = 5000,
  showControls = true,
  showIndicators = true,
  className = ""
}: ImageCarouselProps) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (!isPlaying || isHovered) return;

    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, interval);

    return () => clearInterval(timer);
  }, [slides.length, interval, isPlaying, isHovered]);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  if (!slides.length) return null;

  return (
    <div 
      className={`carousel-container ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative h-48 sm:h-64 md:h-72 lg:h-80 overflow-hidden rounded-xl bg-gray-900 shadow-lg">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`carousel-slide absolute inset-0 transition-all duration-700 ease-out ${
              index === currentSlide
                ? 'opacity-100 transform translate-x-0'
                : index < currentSlide
                  ? 'opacity-0 transform -translate-x-full'
                  : 'opacity-0 transform translate-x-full'
            }`}
          >
            <img
              src={slide.image}
              alt={slide.title}
              className="w-full h-full object-cover object-center"
              loading="lazy"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
            
            {/* Content Overlay */}
            <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 md:p-6 text-white">
              <div className="max-w-4xl">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="px-2 py-1 bg-primary text-primary-foreground text-xs font-medium rounded-md shadow-sm">
                    {slide.category}
                  </span>
                  <span className="text-xs sm:text-sm text-gray-200">
                    {slide.subtitle}
                  </span>
                </div>
                <h3 className="text-base sm:text-lg md:text-xl lg:text-2xl font-bold mb-2 line-clamp-2 drop-shadow-md">
                  {slide.title}
                </h3>
                <p className="text-xs sm:text-sm md:text-base text-gray-200 line-clamp-2 max-w-2xl drop-shadow-sm">
                  {slide.description}
                </p>
              </div>
            </div>
          </div>
        ))}
        
        {/* Navigation Controls */}
        {showControls && slides.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white border-none h-10 w-10 sm:h-12 sm:w-12"
              onClick={goToPrevious}
            >
              <ChevronLeft className="h-5 w-5 sm:h-6 sm:w-6" />
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white border-none h-10 w-10 sm:h-12 sm:w-12"
              onClick={goToNext}
            >
              <ChevronRight className="h-5 w-5 sm:h-6 sm:w-6" />
            </Button>
            
            {/* Play/Pause Button */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 sm:top-4 right-2 sm:right-4 bg-black/20 hover:bg-black/40 text-white border-none h-8 w-8 sm:h-10 sm:w-10"
              onClick={togglePlayPause}
            >
              {isPlaying ? (
                <Pause className="h-4 w-4 sm:h-5 sm:w-5" />
              ) : (
                <Play className="h-4 w-4 sm:h-5 sm:w-5" />
              )}
            </Button>
          </>
        )}
        
        {/* Indicators */}
        {showIndicators && slides.length > 1 && (
          <div className="carousel-navigation">
            {slides.map((_, index) => (
              <button
                key={index}
                className={`carousel-dot ${
                  index === currentSlide ? 'carousel-dot-active' : 'carousel-dot-inactive'
                }`}
                onClick={() => goToSlide(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
        
        {/* Slide Counter */}
        <div className="absolute top-2 sm:top-4 left-2 sm:left-4 bg-black/20 text-white px-2 py-1 rounded-md text-xs sm:text-sm">
          {currentSlide + 1} / {slides.length}
        </div>
      </div>
    </div>
  );
};

export default ImageCarousel;
