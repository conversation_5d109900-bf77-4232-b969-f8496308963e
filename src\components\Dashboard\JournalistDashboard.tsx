
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import StoryCard from '@/components/StoryCard';
import { mockStories, analyticsData, journalistCarouselSlides } from '@/data/mockData';
import { PlusCircle, TrendingUp, Eye, Heart, BarChart3, Clock } from 'lucide-react';
import ImageCarousel from '@/components/ImageCarousel';
import { useToast } from '@/hooks/use-toast';

const JournalistDashboard = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'stories' | 'drafts'>('overview');
  const { toast } = useToast();

  const myStories = mockStories.filter(story => story.author === 'Tendai Mukamuri');
  const draftStories = myStories.filter(story => story.status === 'draft');
  const publishedStories = myStories.filter(story => story.status === 'published');

  const totalViews = publishedStories.reduce((sum, story) => sum + story.views, 0);
  const totalLikes = publishedStories.reduce((sum, story) => sum + story.likes, 0);

  return (
    <div className="space-y-6 pb-20">
      {/* Header */}
      <div className="gradient-primary text-white p-6 rounded-b-3xl">
        <h1 className="text-2xl font-bold mb-2">Journalist Dashboard</h1>
        <p className="text-blue-100">Share Zimbabwe's stories with the world</p>
      </div>

      {/* Quick Actions */}
      <div className="px-4">
        <div className="grid grid-cols-2 gap-3">
          <Button 
            className="gradient-primary text-white touch-target flex items-center space-x-2"
            onClick={() => toast({
              title: "Story Editor Opening",
              description: "Creating new story draft...",
            })}
          >
            <PlusCircle className="w-4 h-4" />
            <span>New Story</span>
          </Button>
          <Button 
            variant="outline" 
            className="touch-target flex items-center space-x-2"
            onClick={() => toast({
              title: "Analytics Loading",
              description: "Fetching your latest performance data...",
            })}
          >
            <BarChart3 className="w-4 h-4" />
            <span>Analytics</span>
          </Button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="px-4">
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          {[
            { id: 'overview', label: 'Overview' },
            { id: 'stories', label: 'Published' },
            { id: 'drafts', label: 'Drafts' }
          ].map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? 'default' : 'ghost'}
              size="sm"
              className="flex-1 touch-target"
              onClick={() => setActiveTab(tab.id as any)}
            >
              {tab.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'overview' && (
        <div className="px-4 space-y-6">
          {/* Journalist Carousel */}
          <ImageCarousel 
            slides={journalistCarouselSlides}
            className="mb-6"
            autoPlay={true}
            interval={7000}
          />
          
          {/* Performance Cards */}
          <div className="grid grid-cols-2 gap-4">
            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Eye className="w-5 h-5 text-primary" />
                  <div>
                    <p className="text-2xl font-bold">{totalViews.toLocaleString()}</p>
                    <p className="text-xs text-muted-foreground">Total Views</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Heart className="w-5 h-5 text-red-500" />
                  <div>
                    <p className="text-2xl font-bold">{totalLikes}</p>
                    <p className="text-xs text-muted-foreground">Total Likes</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-green-500" />
                  <div>
                    <p className="text-2xl font-bold">{publishedStories.length}</p>
                    <p className="text-xs text-muted-foreground">Published</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Clock className="w-5 h-5 text-orange-500" />
                  <div>
                    <p className="text-2xl font-bold">{draftStories.length}</p>
                    <p className="text-xs text-muted-foreground">Drafts</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Stories</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {publishedStories.slice(0, 3).map((story) => (
                <div key={story.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm line-clamp-1">{story.title}</h4>
                    <p className="text-xs text-muted-foreground">{story.publishedAt}</p>
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <span>{story.views} views</span>
                    <Badge variant="outline" className="text-xs">
                      {story.category}
                    </Badge>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'stories' && (
        <div className="px-4 space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Published Stories ({publishedStories.length})</h2>
            <Button variant="outline" size="sm">Sort</Button>
          </div>
          
          <div className="space-y-4">
            {publishedStories.map((story) => (
              <StoryCard key={story.id} story={story} />
            ))}
          </div>
        </div>
      )}

      {activeTab === 'drafts' && (
        <div className="px-4 space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Draft Stories ({draftStories.length})</h2>
            <Button 
              size="sm" 
              className="gradient-primary"
              onClick={() => toast({
                title: "Creating New Draft",
                description: "Opening story editor...",
              })}
            >
              <PlusCircle className="w-4 h-4 mr-2" />
              New Draft
            </Button>
          </div>
          
          {draftStories.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <PlusCircle className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium mb-2">No drafts yet</h3>
              <p className="text-muted-foreground mb-4">Start writing your next story</p>
              <Button 
                className="gradient-primary"
                onClick={() => toast({
                  title: "Story Editor",
                  description: "Opening new story editor...",
                })}
              >
                Create New Story
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {draftStories.map((story) => (
                <StoryCard key={story.id} story={story} />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default JournalistDashboard;
