
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Story } from '@/data/mockData';
import { Heart, Share2, Eye, MapPin, Clock } from 'lucide-react';

interface StoryCardProps {
  story: Story;
  onClick?: () => void;
}

const StoryCard = ({ story, onClick }: StoryCardProps) => {
  return (
    <Card 
      className="hover:shadow-lg transition-all duration-300 cursor-pointer animate-fade-in"
      onClick={onClick}
    >
      <CardContent className="p-0">
        {story.image && (
          <div className="relative">
            <img 
              src={story.image} 
              alt={story.title}
              className="w-full h-48 object-cover rounded-t-lg"
            />
            <Badge 
              className="absolute top-3 left-3 bg-white/90 text-primary hover:bg-white"
            >
              {story.category}
            </Badge>
            {story.verified && (
              <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 text-xs rounded-full">
                Verified
              </div>
            )}
          </div>
        )}
        
        <div className="p-4 space-y-3">
          <h3 className="font-semibold text-lg line-clamp-2 leading-tight">
            {story.title}
          </h3>
          
          <p className="text-muted-foreground text-sm line-clamp-2">
            {story.summary}
          </p>
          
          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
            <div className="flex items-center space-x-1">
              <img 
                src={story.authorAvatar} 
                alt={story.author}
                className="w-4 h-4 rounded-full"
              />
              <span>{story.author}</span>
            </div>
            
            <div className="flex items-center space-x-1">
              <MapPin className="w-3 h-3" />
              <span>{story.location}</span>
            </div>
            
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>{story.publishedAt}</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex space-x-4 text-xs text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Eye className="w-4 h-4" />
                <span>{story.views}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Heart className="w-4 h-4" />
                <span>{story.likes}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Share2 className="w-4 h-4" />
                <span>{story.shares}</span>
              </div>
            </div>
            
            <Badge 
              variant={story.status === 'published' ? 'default' : 'secondary'}
              className="text-xs"
            >
              {story.status}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StoryCard;
