
import { useState, useEffect } from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider, useAuth } from '@/hooks/useAuth';
import SplashScreen from '@/components/SplashScreen';
import AuthForms from '@/components/AuthForms';
import BottomNavigation from '@/components/BottomNavigation';
import ViewerDashboard from '@/components/Dashboard/ViewerDashboard';
import JournalistDashboard from '@/components/Dashboard/JournalistDashboard';
import AdminDashboard from '@/components/Dashboard/AdminDashboard';
import ChatbotFAB from '@/components/ChatbotFAB';
import { Button } from '@/components/ui/button';
import { AlertTriangle, LogOut, Settings, Moon, Sun, Globe } from 'lucide-react';
import { mockNotifications } from '@/data/mockData';
import { useToast } from '@/hooks/use-toast';

const queryClient = new QueryClient();

const AppContent = () => {
  const [showSplash, setShowSplash] = useState(true);
  const [activeTab, setActiveTab] = useState('home');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const { user, logout } = useAuth();
  const { toast } = useToast();

  const unreadNotifications = mockNotifications.filter(n => !n.read).length;

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      toast({
        title: "Back Online",
        description: "Your connection has been restored.",
        duration: 3000,
      });
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast({
        title: "Connection Lost",
        description: "You're now offline. Some features may be limited.",
        variant: "destructive",
        duration: 5000,
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [toast]);

  // Handle dark mode
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkMode]);

  const handlePanicButton = () => {
    toast({
      title: "Panic Alert Activated",
      description: "Emergency protocol initiated. Your safety contacts have been notified.",
      variant: "destructive",
      duration: 10000,
    });
    
    // In a real app, this would trigger safety protocols
    console.log('PANIC BUTTON ACTIVATED - Emergency protocols should be triggered');
  };

  const handleLanguageChange = (lang: string) => {
    setCurrentLanguage(lang);
    toast({
      title: "Language Changed",
      description: `Interface language changed to ${lang === 'en' ? 'English' : lang === 'sn' ? 'Shona' : 'Ndebele'}`,
      duration: 2000,
    });
  };

  const renderDashboard = () => {
    if (!user) return null;

    switch (user.role) {
      case 'admin':
        return <AdminDashboard />;
      case 'journalist':
        return <JournalistDashboard />;
      case 'viewer':
      default:
        return <ViewerDashboard />;
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'home':
        return renderDashboard();
      case 'explore':
        return (
          <div className="container-base44 py-6 pb-20">
            <div className="text-center py-16">
              <Globe className="w-16 h-16 text-primary mx-auto mb-4" />
              <h2 className="text-display-xs font-bold mb-4">Explore Zimbabwe</h2>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Discover stories from across all provinces and communities in Zimbabwe.
              </p>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
                {['Harare', 'Bulawayo', 'Mutare', 'Gweru', 'Masvingo', 'Chinhoyi'].map((city) => (
                  <Button key={city} variant="outline" className="h-12">
                    {city}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        );
      case 'create':
        return (
          <div className="container-base44 py-6 pb-20">
            <div className="text-center py-16">
              <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <AlertTriangle className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-display-xs font-bold mb-4">Share Your Story</h2>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Your voice matters. Share important news and stories from your community.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="gradient-primary">
                  Write Story
                </Button>
                <Button variant="outline" size="lg">
                  Report Issue
                </Button>
              </div>
            </div>
          </div>
        );
      case 'notifications':
        return (
          <div className="container-base44 py-6 pb-20">
            <div className="max-w-2xl mx-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-display-xs font-bold">Notifications</h2>
                <Button variant="ghost" size="sm">
                  Mark all read
                </Button>
              </div>
              
              <div className="space-y-3">
                {mockNotifications.map((notification) => (
                  <div 
                    key={notification.id} 
                    className={`card-base44 p-4 transition-all duration-200 hover:shadow-base44-lg ${
                      !notification.read ? 'border-l-4 border-l-primary bg-blue-50/50' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`w-3 h-3 rounded-full mt-2 ${
                        !notification.read ? 'bg-primary' : 'bg-gray-300'
                      }`} />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <h3 className="font-semibold text-sm truncate pr-2">{notification.title}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            notification.type === 'success' ? 'bg-green-100 text-green-700' :
                            notification.type === 'warning' ? 'bg-yellow-100 text-yellow-700' :
                            notification.type === 'error' ? 'bg-red-100 text-red-700' :
                            'bg-blue-100 text-blue-700'
                          }`}>
                            {notification.type}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                        <p className="text-xs text-muted-foreground mt-2">{notification.timestamp}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      case 'profile':
        return (
          <div className="container-base44 py-6 pb-20">
            <div className="max-w-md mx-auto">
              {/* Profile Header */}
              <div className="text-center mb-8">
                <div className="relative">
                  <img 
                    src={user?.avatar} 
                    alt={user?.name}
                    className="w-24 h-24 rounded-full mx-auto mb-4 shadow-base44-lg"
                  />
                  {user?.role === 'journalist' && (
                    <div className="absolute -bottom-1 -right-1 bg-blue-500 text-white p-1 rounded-full">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
                <h2 className="text-xl font-bold">{user?.name}</h2>
                <p className="text-muted-foreground">{user?.email}</p>
                <div className="flex items-center justify-center space-x-3 mt-3">
                  <span className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium">
                    {user?.role}
                  </span>
                  <span className="text-sm text-muted-foreground">• {user?.location}</span>
                </div>
              </div>
              
              {/* Profile Actions */}
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start h-12">
                  <Settings className="w-5 h-5 mr-3" />
                  Account Settings
                </Button>
                
                <div className="flex space-x-3">
                  <Button 
                    variant="outline" 
                    className="flex-1 justify-start h-12"
                    onClick={() => setIsDarkMode(!isDarkMode)}
                  >
                    {isDarkMode ? <Sun className="w-5 h-5 mr-2" /> : <Moon className="w-5 h-5 mr-2" />}
                    {isDarkMode ? 'Light Mode' : 'Dark Mode'}
                  </Button>
                  
                  <div className="relative">
                    <select 
                      value={currentLanguage}
                      onChange={(e) => handleLanguageChange(e.target.value)}
                      className="appearance-none h-12 pl-10 pr-8 border border-input rounded-lg bg-background text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      <option value="en">English</option>
                      <option value="sn">Shona</option>
                      <option value="nd">Ndebele</option>
                    </select>
                    <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground pointer-events-none" />
                  </div>
                </div>
                
                <Button variant="outline" className="w-full justify-start h-12">
                  Privacy & Safety
                </Button>
                <Button variant="outline" className="w-full justify-start h-12">
                  Help & Support
                </Button>
                <Button variant="outline" className="w-full justify-start h-12">
                  About VoiceGuard+
                </Button>
                
                <Button 
                  variant="destructive" 
                  className="w-full justify-start h-12 mt-6"
                  onClick={logout}
                >
                  <LogOut className="w-5 h-5 mr-3" />
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        );
      default:
        return renderDashboard();
    }
  };

  if (showSplash) {
    return <SplashScreen onComplete={() => setShowSplash(false)} />;
  }

  if (!user) {
    return <AuthForms />;
  }

  return (
    <div className="min-h-screen bg-background relative">
      {/* Offline Banner */}
      {!isOnline && (
        <div className="fixed top-0 left-0 right-0 bg-yellow-500 text-yellow-900 px-4 py-2 text-center text-sm font-medium z-50">
          You're offline. Some features may be limited.
        </div>
      )}

      {/* Panic Button */}
      <div className="fixed top-4 right-4 z-40">
        <Button 
          size="sm" 
          className="bg-red-600 hover:bg-red-700 text-white shadow-base44-lg animate-pulse touch-target"
          onClick={handlePanicButton}
        >
          <AlertTriangle className="w-4 h-4 mr-2" />
          <span className="hidden sm:inline">Panic</span>
        </Button>
      </div>

      {/* Main Content */}
      <main className={`min-h-screen ${!isOnline ? 'pt-10' : ''}`}>
        {renderTabContent()}
      </main>

      {/* Bottom Navigation */}
      <BottomNavigation 
        activeTab={activeTab}
        onTabChange={setActiveTab}
        notificationCount={unreadNotifications}
      />
      
      {/* Chatbot FAB */}
      <ChatbotFAB />
    </div>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <Toaster />
        <Sonner />
        <AppContent />
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
