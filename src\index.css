
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Manrope:wght@400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base44 Design System Implementation */

@layer base {
  :root {
    /* Base44 Color Palette */
    --background: 0 0% 100%;
    --foreground: 220 13% 9%;
    
    --card: 0 0% 100%;
    --card-foreground: 220 13% 9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 9%;
    
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;
    
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 13% 9%;
    
    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;
    
    --accent: 220 14% 96%;
    --accent-foreground: 220 13% 9%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 221 83% 53%;
    
    --radius: 0.75rem;
    
    /* Base44 Specific Colors */
    --blue-50: 240 100% 98%;
    --blue-100: 240 100% 95%;
    --blue-500: 221 83% 53%;
    --blue-600: 221 83% 48%;
    --blue-700: 221 83% 43%;
    --blue-900: 221 39% 11%;
    
    --gray-50: 220 14% 98%;
    --gray-100: 220 14% 96%;
    --gray-200: 220 13% 91%;
    --gray-300: 220 9% 82%;
    --gray-400: 220 9% 64%;
    --gray-500: 220 9% 46%;
    --gray-600: 220 13% 35%;
    --gray-700: 220 13% 26%;
    --gray-800: 220 13% 18%;
    --gray-900: 220 13% 9%;
  }

  .dark {
    --background: 220 13% 9%;
    --foreground: 0 0% 98%;
    --card: 220 13% 12%;
    --card-foreground: 0 0% 98%;
    --popover: 220 13% 12%;
    --popover-foreground: 0 0% 98%;
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;
    --secondary: 220 13% 15%;
    --secondary-foreground: 0 0% 98%;
    --muted: 220 13% 15%;
    --muted-foreground: 220 9% 64%;
    --accent: 220 13% 15%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 13% 18%;
    --input: 220 13% 18%;
    --ring: 221 83% 53%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Manrope', sans-serif;
    font-weight: 600;
  }
}

@layer utilities {
  /* Base44 Typography Scale */
  .text-display-2xl {
    font-size: 4.5rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
  }
  
  .text-display-xl {
    font-size: 3.75rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
  }
  
  .text-display-lg {
    font-size: 3rem;
    line-height: 1.17;
    letter-spacing: -0.02em;
  }
  
  .text-display-md {
    font-size: 2.25rem;
    line-height: 1.22;
    letter-spacing: -0.02em;
  }
  
  .text-display-sm {
    font-size: 1.875rem;
    line-height: 1.27;
    letter-spacing: -0.02em;
  }
  
  .text-display-xs {
    font-size: 1.5rem;
    line-height: 1.33;
  }
  
  /* Base44 Gradients */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(221 83% 43%) 100%);
  }
  
  .gradient-card {
    background: linear-gradient(135deg, hsl(var(--blue-50)) 0%, hsl(var(--background)) 100%);
  }
  
  .gradient-hero {
    background: linear-gradient(135deg, hsl(221 83% 53%) 0%, hsl(221 83% 43%) 50%, hsl(221 39% 11%) 100%);
  }
  
  /* Base44 Shadows */
  .shadow-base44-sm {
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  }
  
  .shadow-base44 {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }
  
  .shadow-base44-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }
  
  .shadow-base44-xl {
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }
  
  /* Animations */
  .animate-fade-in {
    animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
  
  .animate-slide-up {
    animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
  
  .animate-slide-in-right {
    animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
  
  /* Responsive utilities */
  .container-base44 {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .grid-base44 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
  
  .card-base44 {
    @apply bg-card border border-border rounded-xl shadow-base44 hover:shadow-base44-lg transition-all duration-300;
  }
  
  .button-base44 {
    @apply inline-flex items-center justify-center gap-2 rounded-lg px-4 py-2.5 text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }
  
  .input-base44 {
    @apply flex h-11 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }
  
  /* Touch targets for mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(32px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(32px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading animations */
.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid hsl(var(--muted));
  border-top: 3px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Carousel specific styles */
.carousel-container {
  @apply relative overflow-hidden rounded-xl;
}

.carousel-track {
  @apply flex transition-transform duration-500 ease-out;
}

.carousel-slide {
  @apply min-w-full relative;
}

.carousel-navigation {
  @apply absolute inset-x-0 bottom-4 flex justify-center space-x-2;
}

.carousel-dot {
  @apply w-2 h-2 rounded-full transition-all duration-300 cursor-pointer;
}

.carousel-dot-active {
  @apply bg-white shadow-lg;
}

.carousel-dot-inactive {
  @apply bg-white/50 hover:bg-white/75;
}

/* Mobile-first responsive breakpoints */
@media (min-width: 640px) {
  .container-base44 {
    @apply px-6;
  }
}

@media (min-width: 1024px) {
  .container-base44 {
    @apply px-8;
  }
}
