
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { analyticsData, mockStories, adminCarouselSlides } from '@/data/mockData';
import { Users, FileText, AlertTriangle, TrendingUp, Search, Shield, Settings } from 'lucide-react';
import ImageCarousel from '@/components/ImageCarousel';
import { useToast } from '@/hooks/use-toast';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'content' | 'alerts'>('overview');
  const [emergencyMode, setEmergencyMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  const pendingStories = mockStories.filter(story => story.status === 'pending');
  const publishedStories = mockStories.filter(story => story.status === 'published');

  const mockUsers = [
    { id: '1', name: 'Tendai Mukamuri', role: 'journalist', location: 'Harare', status: 'active', stories: 12 },
    { id: '2', name: 'Grace Sibanda', role: 'journalist', location: 'Bulawayo', status: 'active', stories: 8 },
    { id: '3', name: 'Michael Chivayo', role: 'viewer', location: 'Mutare', status: 'active', stories: 0 },
  ];

  return (
    <div className="space-y-6 pb-20">
      {/* Header */}
      <div className="gradient-primary text-white p-6 rounded-b-3xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">Admin Control Center</h1>
            <p className="text-blue-100">Manage VoiceGuard+ platform</p>
          </div>
          <div className="flex items-center space-x-2">
            <Shield className="w-6 h-6" />
            <div className="text-right">
              <p className="text-xs text-blue-100">Security Status</p>
              <p className="text-sm font-medium">All Systems Active</p>
            </div>
          </div>
        </div>
      </div>

      {/* Emergency Alert Toggle */}
      <div className="px-4">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <div>
                  <h3 className="font-medium text-red-900">Emergency Alert System</h3>
                  <p className="text-sm text-red-700">Broadcast urgent safety alerts to all users</p>
                </div>
              </div>
              <Switch
                checked={emergencyMode}
                onCheckedChange={(checked) => {
                  setEmergencyMode(checked);
                  toast({
                    title: checked ? "Emergency Mode Activated" : "Emergency Mode Deactivated",
                    description: checked ? "All users will receive safety alerts" : "Emergency broadcasting disabled",
                    variant: checked ? "destructive" : "default"
                  });
                }}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Navigation Tabs */}
      <div className="px-4">
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 overflow-x-auto">
          {[
            { id: 'overview', label: 'Overview', icon: TrendingUp },
            { id: 'users', label: 'Users', icon: Users },
            { id: 'content', label: 'Content', icon: FileText },
            { id: 'alerts', label: 'Alerts', icon: AlertTriangle }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <Button
                key={tab.id}
                variant={activeTab === tab.id ? 'default' : 'ghost'}
                size="sm"
                className="flex-shrink-0 touch-target flex items-center space-x-2"
                onClick={() => setActiveTab(tab.id as any)}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </Button>
            );
          })}
        </div>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'overview' && (
        <div className="px-4 space-y-6">
          {/* Admin Carousel */}
          <ImageCarousel 
            slides={adminCarouselSlides}
            className="mb-6"
            autoPlay={true}
            interval={6000}
          />
          
          {/* Analytics Cards */}
          <div className="grid grid-cols-2 gap-4">
            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">{analyticsData.totalUsers.value.toLocaleString()}</p>
                    <p className="text-xs text-muted-foreground">Total Users</p>
                  </div>
                  <div className="text-right">
                    <p className={`text-xs ${analyticsData.totalUsers.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {analyticsData.totalUsers.growth > 0 ? '+' : ''}{analyticsData.totalUsers.growth}%
                    </p>
                    <Users className="w-5 h-5 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">{analyticsData.activeStories.value}</p>
                    <p className="text-xs text-muted-foreground">Active Stories</p>
                  </div>
                  <div className="text-right">
                    <p className={`text-xs ${analyticsData.activeStories.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      +{analyticsData.activeStories.growth}%
                    </p>
                    <FileText className="w-5 h-5 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">{analyticsData.reportsToday.value}</p>
                    <p className="text-xs text-muted-foreground">Reports Today</p>
                  </div>
                  <div className="text-right">
                    <p className={`text-xs ${analyticsData.reportsToday.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {analyticsData.reportsToday.growth}%
                    </p>
                    <AlertTriangle className="w-5 h-5 text-orange-500" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="gradient-card">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">{analyticsData.engagement.value}%</p>
                    <p className="text-xs text-muted-foreground">Engagement</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-green-600">+{analyticsData.engagement.growth}%</p>
                    <TrendingUp className="w-5 h-5 text-green-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Platform Activity</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm">New story published</p>
                  <p className="text-xs text-muted-foreground">Tendai Mukamuri - Community Water Project</p>
                </div>
                <Badge className="bg-green-100 text-green-800">Published</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm">New user registered</p>
                  <p className="text-xs text-muted-foreground">Journalist from Gweru</p>
                </div>
                <Badge className="bg-blue-100 text-blue-800">New User</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm">Story pending review</p>
                  <p className="text-xs text-muted-foreground">Grace Sibanda - Business Revival</p>
                </div>
                <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'users' && (
        <div className="px-4 space-y-4">
          <div className="flex items-center space-x-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 touch-target"
              />
            </div>
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
          </div>

          <div className="space-y-3">
            {mockUsers.map((user) => (
              <Card key={user.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white font-medium">
                        {user.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div>
                        <h3 className="font-medium">{user.name}</h3>
                        <p className="text-sm text-muted-foreground">{user.location}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant={user.role === 'journalist' ? 'default' : 'secondary'}>
                        {user.role}
                      </Badge>
                      <p className="text-xs text-muted-foreground mt-1">
                        {user.stories} stories
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'content' && (
        <div className="px-4 space-y-4">
          <div className="grid grid-cols-3 gap-2 text-center">
            <div className="p-3 bg-yellow-50 rounded-lg">
              <p className="text-2xl font-bold text-yellow-600">{pendingStories.length}</p>
              <p className="text-xs text-yellow-800">Pending</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">{publishedStories.length}</p>
              <p className="text-xs text-green-800">Published</p>
            </div>
            <div className="p-3 bg-red-50 rounded-lg">
              <p className="text-2xl font-bold text-red-600">2</p>
              <p className="text-xs text-red-800">Flagged</p>
            </div>
          </div>

          <h3 className="text-lg font-semibold">Content Moderation Queue</h3>
          
          <div className="space-y-3">
            {mockStories.slice(0, 3).map((story) => (
              <Card key={story.id}>
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium line-clamp-1">{story.title}</h4>
                        <p className="text-sm text-muted-foreground line-clamp-2">{story.summary}</p>
                        <div className="flex items-center space-x-2 mt-2 text-xs text-muted-foreground">
                          <span>{story.author}</span>
                          <span>•</span>
                          <span>{story.location}</span>
                          <span>•</span>
                          <span>{story.category}</span>
                        </div>
                      </div>
                      <Badge variant={story.verified ? 'default' : 'secondary'}>
                        {story.verified ? 'Verified' : 'Unverified'}
                      </Badge>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button 
                        size="sm" 
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                        onClick={() => toast({
                          title: "Story Approved",
                          description: "Story has been published successfully",
                        })}
                      >
                        Approve
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="flex-1"
                        onClick={() => toast({
                          title: "Story Under Review",
                          description: "Story moved to detailed review queue",
                        })}
                      >
                        Review
                      </Button>
                      <Button 
                        size="sm" 
                        variant="destructive" 
                        className="flex-1"
                        onClick={() => toast({
                          title: "Story Rejected",
                          description: "Story removed from publication queue",
                          variant: "destructive"
                        })}
                      >
                        Reject
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
