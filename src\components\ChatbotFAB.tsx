import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { X, Send, MessageSquare } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const ChatbotFAB = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([
    {
      id: '1',
      text: 'Hi! I\'m VoiceGuard Assistant. How can I help you stay safe while reporting?',
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const { toast } = useToast();

  const handleSendMessage = () => {
    if (!message.trim()) return;

    const newMessage = {
      id: Date.now().toString(),
      text: message,
      sender: 'user' as const,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setMessage('');

    // Simulate bot response
    setTimeout(() => {
      const botResponse = {
        id: (Date.now() + 1).toString(),
        text: getBotResponse(message),
        sender: 'bot' as const,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    }, 1000);

    toast({
      title: "Message sent",
      description: "VoiceGuard Assistant is responding...",
    });
  };

  const getBotResponse = (userMessage: string) => {
    const msg = userMessage.toLowerCase();
    if (msg.includes('safety') || msg.includes('danger')) {
      return 'For immediate safety concerns, use the Panic Button in the top right. For general safety tips, avoid sharing your location in real-time and always inform trusted contacts about your reporting activities.';
    } else if (msg.includes('story') || msg.includes('publish')) {
      return 'To publish a story, go to your dashboard and click "New Story". Make sure to verify your sources and add relevant location tags to help your community.';
    } else if (msg.includes('verify') || msg.includes('fact')) {
      return 'Always cross-reference information with multiple sources. Use our verification tools in the story editor to mark content as verified.';
    } else {
      return 'I can help with story publishing, safety guidelines, platform features, and journalism best practices. What would you like to know more about?';
    }
  };

  return (
    <>
      {/* Floating Action Button */}
      <Button
        className="fixed bottom-24 right-6 w-14 h-14 rounded-full gradient-primary shadow-lg z-50 touch-target"
        onClick={() => setIsOpen(true)}
        size="icon"
      >
        <img
          src="/assets/voiceguard-logo.png"
          alt="VoiceGuard Assistant"
          className="w-8 h-8"
        />
      </Button>

      {/* Chat Dialog */}
      {isOpen && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-end sm:items-center sm:justify-center p-4">
          <Card className="w-full max-w-md bg-white rounded-t-3xl sm:rounded-3xl max-h-[80vh] flex flex-col">
            <CardHeader className="gradient-primary text-white rounded-t-3xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <img
                    src="/assets/voiceguard-logo.png"
                    alt="VoiceGuard"
                    className="w-8 h-8"
                  />
                  <div>
                    <CardTitle className="text-lg">VoiceGuard Assistant</CardTitle>
                    <p className="text-blue-100 text-sm">Here to help you report safely</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(false)}
                  className="text-white hover:bg-white/20"
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="flex-1 p-4 overflow-hidden flex flex-col">
              {/* Messages */}
              <div className="flex-1 overflow-y-auto space-y-3 mb-4 max-h-96">
                {messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-2xl ${
                        msg.sender === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="text-sm">{msg.text}</p>
                      <p className="text-xs opacity-70 mt-1">
                        {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Input */}
              <div className="flex space-x-2">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Ask about safety, stories, or features..."
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  className="flex-1"
                />
                <Button 
                  onClick={handleSendMessage}
                  size="icon"
                  className="gradient-primary shrink-0"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>

              {/* Quick Actions */}
              <div className="flex flex-wrap gap-2 mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setMessage('How do I stay safe while reporting?')}
                  className="text-xs"
                >
                  Safety Tips
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setMessage('How do I publish a story?')}
                  className="text-xs"
                >
                  Publish Story
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setMessage('How do I verify information?')}
                  className="text-xs"
                >
                  Verify Info
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
};

export default ChatbotFAB;