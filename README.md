# VoiceGuard+ Zimbabwe Pulse

## Project Overview

VoiceGuard+ is a secure journalism platform designed specifically for Zimbabwe's media landscape. It provides journalists, citizens, and viewers with a safe, verified platform for sharing and consuming local news and stories.

## Features

- **Secure Journalism Platform**: Safe environment for journalists to publish stories
- **Community Engagement**: Citizen journalism and community-driven content
- **Verification System**: Built-in fact-checking and story verification
- **Multi-language Support**: English, Shona, and Ndebele language support
- **Offline Capabilities**: Works even with limited internet connectivity
- **Safety Features**: Panic button and emergency protocols for journalist safety

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager

### Installation

1. Clone this repository
2. Install dependencies:

```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:8080`

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── Dashboard/      # Dashboard components
│   └── ...
├── hooks/              # Custom React hooks
├── data/               # Mock data and types
├── lib/                # Utility functions
└── index.css           # Global styles
```

## Technology Stack

- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **State Management**: React hooks and context
- **Icons**: Lucide React
- **Notifications**: Sonner

## Deployment

This project can be deployed to any static hosting service such as:

- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront

### Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please open an issue in the GitHub repository.
